<template>
    <el-dialog
        v-model="dialogVisible"
        :title="'专利详情'"
        @close="handleClose()"
        width="60%" 
    >
        <div v-if="loading" class="text-center all-padding-24">
            <i class="el-icon-loading"></i> 正在加载专利信息...
        </div>
        <div
            v-else
            class="h-500 overflow-y-auto">
            <div class="flex">
                <div class='w-180 h-200 border'>
                    <img :src="row.lprs" alt="专利图片">
                </div>
                <div class="l-margin-16 width-100">
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >专利名称</el-col>
                        <el-col :span="18" class="all-padding-16 border"
                        >XXX</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >专利类型</el-col>
                        <el-col :span="18" class="all-padding-16 border"
                        >XXX</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >申请号</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >XXX</el-col>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >申请日期</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >XXX</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >公开（公告）号</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >XXX</el-col>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >公开（公告）日期</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >XXX</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >优先权号</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >XXX</el-col>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >优先权日</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >XXX</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >发明人</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >XXX</el-col>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >申请人</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >XXX</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >地址</el-col>
                        <el-col :span="18" class="all-padding-16 border"
                        >XXX</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >代理机构</el-col>
                        <el-col :span="18" class="all-padding-16 border"
                        >XXX</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >代理人</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >XXX</el-col>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >IPC分类号</el-col>
                        <el-col :span="6" class="all-padding-16 border"
                        >XXX</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="6" class="all-padding-16 border bg-grey"
                        >摘要</el-col>
                        <el-col :span="18" class="all-padding-16 border"
                        >XXX</el-col>
                    </el-row>
                </div>
            </div>
            <div class="width-100 h-1 border tb-margin-16"></div>
            <div class="">
                <span class="font-20">法律状态</span>
                <div class="width-100 t-margin-16">
                    <el-row>
                        <el-col :span="8" class="all-padding-16 border bg-grey"
                        >法律状态公告日</el-col>
                        <el-col :span="8" class="all-padding-16 border bg-grey"
                        >法律状态</el-col>
                        <el-col :span="8" class="all-padding-16 border bg-grey"
                        >法律状态信息</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="8" class="all-padding-16 border"
                        >XXX</el-col>
                        <el-col :span="8" class="all-padding-16 border"
                        >XXX</el-col>
                        <el-col :span="8" class="all-padding-16 border"
                        >XXX</el-col>
                    </el-row>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script lang='ts' setup>
import { ref, watch,} from 'vue'
const loading = ref(true)
const props = defineProps<{
    visible: boolean;
    row:IPatenetDetailParams;
}>()
const dialogVisible = ref<boolean>(props.visible)
console.log('props.visible', props.visible)
interface IPatenetDetailParams {
    PPLYDATE: string
    APPLYNO: string
    APPLYPUBDATE: string
    APPLYPUBNO: string
    NOTICETYPE: string
    PATENTNAME: string
    lprs?: string
}

watch(() => props.visible, async (val) => {
    console.log('visible', val)
    dialogVisible.value = val
    if (val) {
        setTimeout(() => {
            loading.value = false
        },2000)
        // await getAnnualReport()
    }
}, { immediate: true })
const emit = defineEmits(['update:visible'])
const handleClose = () => {
    dialogVisible.value = false
    loading.value = true
    emit('update:visible', false)
}
</script>

<style lang='scss' scoped>

.bg-grey{
    background-color: #F5F7FA;
}
</style>